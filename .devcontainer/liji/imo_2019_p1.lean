-- Proof content:
-- 1. [Problem Restatement] Find all functions f : ℤ → ℤ that satisfy f(2a) + 2f(b) = f( f(a + b) ) for every pair of integers a, b. 2. [Key Idea] Isolate f(0)=c, turn the equation into an additive relation, and then reduce to a linear (Cauchy-type) function whose slope is forced to be 0 or 2. 3. [Proof] Step 1. Put a = 0: c + 2f(b) = f(f(b)) ∀ b. (1) Step 2. Put b = 0: f(2a) + 2c = f(f(a)) ∀ a. (2) Using (1) in (2) gives f(2a) = 2f(a) – c ∀ a. (3) Step 3. Substitute (1) and (3) back into the original equation: (2f(a) – c) + 2f(b) = 2f(a + b) + c. Hence f(a + b) = f(a) + f(b) – c ∀ a, b. (4) Step 4. Let g(x) = f(x) – c. Then (4) becomes the Cauchy equation on ℤ: g(a + b) = g(a) + g(b). Therefore g(x) = d x for some integer constant d, and f(x) = d x + c. (5) Step 5. Insert (5) into the original functional equation: Left = d(2a) + c + 2(d b + c) = 2d(a + b) + 3c, Right = d( d(a + b) + c ) + c = d²(a + b) + dc + c. Comparing coefficients for all a, b and the constant term yields d(d – 2) = 0 and (d – 2)c = 0. Thus • d = 0 ⇒ c = 0 ⇒ f(x) = 0, • d = 2 ⇒ c arbitrary ⇒ f(x) = 2x + c. No other possibilities exist. 4. [Conclusion] The functional equation admits exactly two types of solutions: (i) the zero function f(x) ≡ 0, and (ii) the affine family f(x) = 2x + c with an arbitrary integer constant c.
