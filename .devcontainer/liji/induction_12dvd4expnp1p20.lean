-- Proof content:
-- 1. [Problem Restatement] Prove that every natural number n satisfies 12 | (4^{\,n+1}+20). 2. [Key Idea] Check divisibility separately modulo 4 and modulo 3 (their coprime factors); if a number is a multiple of both, it is a multiple of 12. 3. [Proof] (i) Divisibility by 4 4^{\,n+1} is a power of 4, hence a multiple of 4, and 20 ≡ 0 (mod 4); therefore 4^{\,n+1}+20 ≡ 0 (mod 4). (ii) Divisibility by 3 Note 4 ≡ 1 (mod 3). Hence 4^{\,n+1} ≡ 1^{\,n+1} ≡ 1 (mod 3). Thus 4^{\,n+1}+20 ≡ 1+20 ≡ 21 ≡ 0 (mod 3). (iii) Combine Since 4 and 3 are coprime and the expression is divisible by both, it is divisible by their product 12. 4. [Conclusion] Therefore, for every natural number n, the integer 4^{\,n+1}+20 is a multiple of 12.
