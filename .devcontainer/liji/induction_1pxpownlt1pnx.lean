-- Proof content:
-- 1. [Problem Restatement] Prove that for every integer n ≥ 1 and every real x > –1, the inequality (1 + nx) ≤ (1 + x)ⁿ holds. 2. [Key Idea] This is <PERSON><PERSON><PERSON>’s inequality; a short induction on n (or, when x ≥ 0, the binomial expansion) establishes the result. 3. [Proof] Proof 1 – Induction on n • Base case n = 1: (1 + 1·x) = 1 + x = (1 + x)¹. • Induction step: assume (1 + nx) ≤ (1 + x)ⁿ. Then (1 + (n + 1)x) = (1 + nx) + x ≤ (1 + x)ⁿ + x. We must show (1 + x)ⁿ + x ≤ (1 + x)ⁿ(1 + x). Subtract (1 + x)ⁿ: this is equivalent to x ≤ x(1 + x)ⁿ. Because x > –1, two cases finish the argument: – If x ≥ 0, then (1 + x)ⁿ ≥ 1, so x ≤ x(1 + x)ⁿ. – If –1 < x < 0, then (1 + x)ⁿ ≤ 1 and x ≤ x(1 + x)ⁿ holds since both sides are negative and the inequality reverses on multiplying by the negative x. Hence the inequality is preserved, completing the induction. Proof 2 – Binomial expansion (x ≥ 0) For x ≥ 0, expand: (1 + x)ⁿ = 1 + nx + C(n,2)x² + … + C(n,n)xⁿ ≥ 1 + nx, since every remaining term is non-negative. Therefore (1 + nx) ≤ (1 + x)ⁿ. 4. [Conclusion] Thus, for all real x > –1 and integers n ≥ 1, the inequality (1 + nx) ≤ (1 + x)ⁿ is valid.
